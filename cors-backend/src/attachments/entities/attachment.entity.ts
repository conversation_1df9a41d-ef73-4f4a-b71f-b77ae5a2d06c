import { Entity, Column, Index, ManyToOne } from 'typeorm';
import { BaseEntity } from 'src/common/base.entity';
import { ApiProperty } from '@nestjs/swagger';
import { LineItem } from '../../orders/entities/line-item.entity';
import { LineItemRequest } from '../../orders/entities/line-item-request.entity';

export enum AttachmentStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected'
}

@Entity('attachments')
@Index(['attachableType', 'attachableId'])
export class Attachment extends BaseEntity {
  @ApiProperty({ description: 'The name of the uploaded file' })
  @Column()
  filename: string;

  @ApiProperty({ description: 'The URL where the file can be accessed' })
  @Column()
  url: string;

  @ApiProperty({ description: 'The MIME type of the file' })
  @Column()
  mimetype: string;

  @ApiProperty({ description: 'The size of the file in bytes' })
  @Column()
  size: number;

  @ApiProperty({ description: 'The type of entity this attachment is associated with' })
  @Column()
  attachableType: string;

  @ApiProperty({ description: 'The ID of the entity this attachment is associated with' })
  @Column()
  attachableId: string;

  @ApiProperty({ 
    description: 'The current status of the attachment',
    enum: AttachmentStatus,
    example: AttachmentStatus.PENDING
  })
  @Column({
    type: 'enum',
    enum: AttachmentStatus,
    default: AttachmentStatus.PENDING
  })
  status: AttachmentStatus;

  @ManyToOne(() => LineItem, lineItem => lineItem.attachments, { nullable: true })
  lineItem: LineItem;

  @ManyToOne(() => LineItemRequest, request => request.attachments, { nullable: true })
  request: LineItemRequest;
}
