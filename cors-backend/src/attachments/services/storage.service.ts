import { Injectable, Inject } from '@nestjs/common';
import { StorageStrategy, STORAGE_STRATEGY } from '../strategies/storage.strategy';
import { Multer } from 'multer';

@Injectable()
export class StorageService {
  constructor(
    @Inject(STORAGE_STRATEGY)
    private readonly storageStrategy: StorageStrategy
  ) { }

  async uploadFile(file: Multer["File"]) {
    return this.storageStrategy.uploadFile(file);
  }

  async deleteFile(filename: string) {
    return this.storageStrategy.deleteFile(filename);
  }
} 