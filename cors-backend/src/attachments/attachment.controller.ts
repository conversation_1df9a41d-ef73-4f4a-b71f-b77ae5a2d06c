import {
  Controller,
  Post,
  UploadedFile,
  UseInterceptors,
  Body,
  Get,
  Param,
  Delete,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Multer } from 'multer';
import { AttachmentService } from './attachment.service';

@Controller('attachments')
export class AttachmentController {
  constructor(
    private readonly attachmentService: AttachmentService
  ) { }

  @Post('upload-image')
  @UseInterceptors(FileInterceptor('file'))
  async uploadImage(@UploadedFile() file: Multer.File) {
    return this.attachmentService.uploadImage(file);
  }

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  async upload(
    @UploadedFile() file: Multer.File,
    @Body('attachableType') attachableType: string,
    @Body('attachableId') attachableId: string,
  ) {
    return this.attachmentService.upload(file, attachableType, attachableId);
  }

  @Get()
  async getAttachments() {
    return this.attachmentService.getAttachments();
  }

  @Delete(':id')
  async deleteAttachment(@Param('id') id: string) {
    return this.attachmentService.delete(id);
  }
}
