{"name": "cors-backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "engines": {"node": "22.x", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.15.4", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "ts-node -r tsconfig-paths/register -r dotenv/config ./node_modules/typeorm/cli.js", "migration:generate": "ts-node -r tsconfig-paths/register -r dotenv/config ./node_modules/typeorm/cli.js migration:generate -d db/data-source.ts", "migration:run:dev": "ts-node -r tsconfig-paths/register -r dotenv/config ./node_modules/typeorm/cli.js migration:run -d db/data-source.ts", "migration:run:prod": "node -r dotenv/config ./node_modules/typeorm/cli.js migration:run -d ./db/data-source.js", "migration:revert:dev": "ts-node -r tsconfig-paths/register -r dotenv/config ./node_modules/typeorm/cli.js migration:revert -d db/data-source.ts", "migration:revert:prod": "node -r dotenv/config ./node_modules/typeorm/cli.js migration:revert -d ./db/data-source.js", "seed:dev": "ts-node -r tsconfig-paths/register -r dotenv/config db/seeds/run-seeds.ts", "preinstall": "npx only-allow pnpm && npm install -g pnpm"}, "dependencies": {"@aws-sdk/client-s3": "^3.812.0", "@bull-board/api": "^6.9.6", "@bull-board/express": "^6.9.6", "@casl/ability": "^6.7.3", "@nestjs/axios": "^4.0.0", "@nestjs/bullmq": "^11.0.2", "@nestjs/common": "^10.4.17", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.4.17", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^10.4.17", "@nestjs/swagger": "^11.2.0", "@nestjs/typeorm": "^10.0.2", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "body-parser": "^2.2.0", "bullmq": "^5.53.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "cloudinary": "^2.6.1", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "express": "^5.1.0", "express-basic-auth": "^1.2.1", "graphql": "^16.11.0", "graphql-request": "^7.1.2", "html-to-text": "^9.0.5", "ioredis": "^5.6.1", "mime-types": "^3.0.1", "multer": "1.4.5-lts.2", "nest-casl": "^1.9.15", "nestjs-cls": "^5.4.3", "nodemailer": "^7.0.3", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "passport-strategy": "^1.0.0", "pg": "^8.16.0", "reflect-metadata": "^0.1.14", "rxjs": "^7.8.2", "swagger-ui-express": "^5.0.1", "typeorm": "^0.3.24", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.27.0", "@nestjs/cli": "^10.4.9", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.1.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.11.24", "@types/axios": "^0.14.4", "@types/bcrypt": "^5.0.2", "@types/express": "^4.17.22", "@types/ioredis": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.15.21", "@types/nodemailer": "^6.4.17", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.3", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "globals": "^16.1.0", "jest": "^29.7.0", "prettier": "^3.5.3", "source-map-support": "^0.5.21", "supertest": "^7.1.1", "ts-jest": "^29.3.4", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}