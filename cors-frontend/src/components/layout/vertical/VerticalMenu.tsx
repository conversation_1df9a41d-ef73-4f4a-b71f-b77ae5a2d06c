import { useTheme } from '@mui/material/styles';
import PerfectScrollbar from 'react-perfect-scrollbar';
import type { VerticalMenuContextProps } from '@menu/components/vertical-menu/Menu';
import { Menu, MenuItem, SubMenu } from '@menu/vertical-menu';
import useVerticalNav from '@menu/hooks/useVerticalNav';
import StyledVerticalNavExpandIcon from '@menu/styles/vertical/StyledVerticalNavExpandIcon';
import menuItemStyles from '@core/styles/vertical/menuItemStyles';
import menuSectionStyles from '@core/styles/vertical/menuSectionStyles';
import { RoleProtected } from '@/components/ProtectedRoleWrapper';
import { Actions, ActionsTarget } from '@/libs/casl/ability';
import { redirect, usePathname } from 'next/navigation';
import { useAbility } from '@/libs/casl/AbilityContext';

type RenderExpandIconProps = {
  open?: boolean;
  transitionDuration?: VerticalMenuContextProps['transitionDuration'];
};

type Props = {
  scrollMenu: (container: any, isPerfectScrollbar: boolean) => void;
};

const RenderExpandIcon = ({ open, transitionDuration }: RenderExpandIconProps) => (
  <StyledVerticalNavExpandIcon open={open} transitionDuration={transitionDuration}>
    <i className="ri-arrow-right-s-line" />
  </StyledVerticalNavExpandIcon>
);

const routeGuards = [
  {
    pathIncludes: 'users',
    action: Actions.ViewUsersListingPage,
    target: ActionsTarget.UserManagment,
    childPermission: [
      {
        action: Actions.EditUser,
        path: 'edit',
      },
      {
        action: Actions.ViewUserDetail,
        path: 'view',
      },
      {
        action: Actions.CreateUsers,
        path: 'add',
      },
    ],
  },
  {
    pathIncludes: 'roles',
    action: Actions.ViewRolesListingPage,
    target: ActionsTarget.RoleManagement,
    childPermission: [
      {
        action: Actions.EditRole,
        path: 'edit',
      },
      {
        action: Actions.ViewRoleDetail,
        path: 'view',
      },
      {
        action: Actions.CreateRole,
        path: 'add',
      },
    ],
  },
  {
    pathIncludes: 'ordermanagement',
    action: Actions.ViewPIMSListingPage,
    target: ActionsTarget.PIMS,
    childPermission: [
      {
        action: Actions.EditDetailPage,
        path: 'edit',
      },
      {
        action: Actions.ViewPIMSListingPage,
        path: 'view',
      },
    ],
  },
  {
    pathIncludes: 'ordermanagement/orders',
    action: Actions.EditOrderDetialPage,
    target: ActionsTarget.ORDERS,
    childPermission: [
      {
        action: Actions.CreateOrder,
        path: 'add',
      },

      {
        action: Actions.EditOrderDetialPage,
        path: 'view',
      },
      {
        action: Actions.EditLineItem,
        path: 'view',
      },
      {
        action: Actions.CancelLineItem,
        path: 'view',
      },
      {
        action: Actions.CreateLineItemRemake,
        path: 'view',
      },
      {
        action: Actions.FlagLineItem,
        path: 'view',
      },
      {
        action: Actions.InitiateLineItemCustomerContactRequest,
        path: 'view',
      },
      {
        action: Actions.UploadLineItemCompletedArtfile,
        path: 'view',
      },
      {
        action: Actions.AddLineItemRevisionRequest,
        path: 'view',
      },
    ],
  },
];
const circleLineIcon = () => <i className="ri-circle-line" style={{ fontSize: 10 }} />;

const VerticalMenu = ({ scrollMenu }: Props) => {
  const theme = useTheme();
  const verticalNavOptions = useVerticalNav();
  const ability = useAbility();
  const path = usePathname();

  const { isBreakpointReached, transitionDuration } = verticalNavOptions;

  const hasLineItemPermissions = () => {
    const hasPermissions =
      ability?.can(Actions.EditLineItem, ActionsTarget.LINE_ITEMS) ||
      ability?.can(Actions.CancelLineItem, ActionsTarget.LINE_ITEMS) ||
      ability?.can(Actions.CreateLineItemRemake, ActionsTarget.LINE_ITEMS) ||
      ability?.can(Actions.FlagLineItem, ActionsTarget.LINE_ITEMS) ||
      ability?.can(Actions.UploadLineItemCompletedArtfile, ActionsTarget.LINE_ITEMS) ||
      ability?.can(Actions.InitiateLineItemCustomerContactRequest, ActionsTarget.LINE_ITEMS) ||
      ability?.can(Actions.AddLineItemRevisionRequest, ActionsTarget.LINE_ITEMS);
    return hasPermissions;
  };

  const isPathProtected = () => {
    // First check if path is for ordermanagement/orders
    if (path.includes('ordermanagement/orders')) {
      // Check for either EditOrderDetialPage or ViewOrderListingPage permissions
      const hasOrderPermission =
        ability?.can(Actions.EditOrderDetialPage, ActionsTarget.ORDERS) ||
        ability?.can(Actions.ViewOrderListingPage, ActionsTarget.ORDERS);
      const hasLineItems = hasLineItemPermissions();
      return !hasOrderPermission && !hasLineItems;
    }

    if (path.includes('ordermanagement') && !path.includes('ordermanagement/orders')) {
      return !ability?.can(Actions.ViewPIMSListingPage, ActionsTarget.PIMS);
    }

    for (const guard of routeGuards) {
      if (guard.pathIncludes.includes('ordermanagement')) {
        continue;
      }

      if (path.includes(guard.pathIncludes)) {
        if (!ability?.can(guard.action, guard.target)) {
          return true;
        }
        for (const childPerm of guard.childPermission || []) {
          if (
            path.includes(`${guard.pathIncludes}/${childPerm.path}`) &&
            !ability?.can(childPerm.action, guard.target)
          ) {
            return true;
          }
        }
      }
    }

    return false;
  };

  if (isPathProtected()) {
    redirect('/');
  }

  const ScrollWrapper = isBreakpointReached ? 'div' : PerfectScrollbar;

  // Custom component to check for either Orders or LINE_ITEMS permissions
  const OrdersMenuItem = () => {
    const ability = useAbility();

    const hasOrderPermission =
      ability?.can(Actions.EditOrderDetialPage, ActionsTarget.ORDERS) ||
      ability?.can(Actions.ViewOrderListingPage, ActionsTarget.ORDERS);
    const hasLineItemPermission =
      ability?.can(Actions.EditLineItem, ActionsTarget.LINE_ITEMS) ||
      ability?.can(Actions.CancelLineItem, ActionsTarget.LINE_ITEMS) ||
      ability?.can(Actions.CreateLineItemRemake, ActionsTarget.LINE_ITEMS) ||
      ability?.can(Actions.FlagLineItem, ActionsTarget.LINE_ITEMS) ||
      ability?.can(Actions.UploadLineItemCompletedArtfile, ActionsTarget.LINE_ITEMS) ||
      ability?.can(Actions.InitiateLineItemCustomerContactRequest, ActionsTarget.LINE_ITEMS) ||
      ability?.can(Actions.AddLineItemRevisionRequest, ActionsTarget.LINE_ITEMS);

    if (hasOrderPermission || hasLineItemPermission) {
      return (
        <MenuItem icon={circleLineIcon()} href="/ordermanagement/orders">
          Orders
        </MenuItem>
      );
    }

    return null;
  };

  return (
    <ScrollWrapper
      {...(isBreakpointReached
        ? {
            className: 'bs-full overflow-y-auto overflow-x-hidden',
            onScroll: container => scrollMenu(container, false),
          }
        : {
            options: { wheelPropagation: false, suppressScrollX: true },
            onScrollY: container => scrollMenu(container, true),
          })}
    >
      <Menu
        popoutMenuOffset={{ mainAxis: 10 }}
        menuItemStyles={menuItemStyles(verticalNavOptions, theme)}
        renderExpandIcon={({ open }) => (
          <RenderExpandIcon open={open} transitionDuration={transitionDuration} />
        )}
        renderExpandedMenuItemIcon={{ icon: <i className="ri-circle-line" /> }}
        menuSectionStyles={menuSectionStyles(verticalNavOptions, theme)}
      >
        <MenuItem href="/home" icon={<i className="ri-home-smile-line" />}>
          Home
        </MenuItem>
        <SubMenu label="Administration" icon={<i className="ri-lock-2-line" />}>
          <RoleProtected
            action={Actions.ViewUsersListingPage}
            actionTarget={ActionsTarget.UserManagment}
          >
            <MenuItem icon={circleLineIcon()} href="/users">
              Users
            </MenuItem>
          </RoleProtected>
          <RoleProtected
            action={Actions.ViewRolesListingPage}
            actionTarget={ActionsTarget.RoleManagement}
          >
            <MenuItem icon={circleLineIcon()} href="/roles">
              Roles
            </MenuItem>
          </RoleProtected>
          <RoleProtected
            action={Actions.ViewRolesListingPage}
            actionTarget={ActionsTarget.RoleManagement}
          >
            <MenuItem icon={circleLineIcon()} href="/settings">
              Settings
            </MenuItem>
          </RoleProtected>
        </SubMenu>
        <SubMenu label="Order Management" icon={<i className="ri-menu-line"></i>}>
          <RoleProtected action={Actions.ViewPIMSListingPage} actionTarget={ActionsTarget.PIMS}>
            <MenuItem icon={circleLineIcon()} href="/ordermanagement/products">
              PIMS
            </MenuItem>
          </RoleProtected>

          <OrdersMenuItem />
          <RoleProtected>
            <MenuItem icon={circleLineIcon()} href="/ordermanagement/queues">
              Queues
            </MenuItem>
          </RoleProtected>
        </SubMenu>
        {/* <MenuItem href='/about' icon={<i className='ri-information-line' />}>
          About
        </MenuItem> */}
      </Menu>
    </ScrollWrapper>
  );
};

export default VerticalMenu;
