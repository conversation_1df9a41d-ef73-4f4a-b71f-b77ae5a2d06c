import { Actions, ActionsTarget } from '@/libs/casl/ability';

/**
 * Utility functions for mapping queue names to their corresponding permissions
 */

export interface QueuePermission {
  action: Actions;
  target: ActionsTarget;
}

/**
 * Get the View permission for a queue by name
 */
export const getViewPermissionForQueue = (queueName: string): QueuePermission | null => {
  switch (queueName) {
    case 'Crop Review':
      return { action: Actions.ViewCropReviewQueue, target: ActionsTarget.CROP_REVIEW_QUEUE };
    case 'Crop Needed':
      return { action: Actions.ViewCropNeededQueue, target: ActionsTarget.CROP_NEEDED_QUEUE };
    case 'Template Placement':
      return {
        action: Actions.ViewTemplatePlacementQueue,
        target: ActionsTarget.TEMPLATE_PLACEMENT_QUEUE,
      };
    case 'Ready For Artwork':
      return { action: Actions.ViewArtworkReadyQueue, target: ActionsTarget.ARTWORK_READY_QUEUE };
    case 'Artwork Revision':
      return {
        action: Actions.ViewArtworkRevisionQueue,
        target: ActionsTarget.ARTWORK_REVISION_QUEUE,
      };
    default:
      return null;
  }
};

/**
 * Get the Start permission for a queue by name
 */
export const getStartPermissionForQueue = (queueName: string): QueuePermission | null => {
  switch (queueName) {
    case 'Crop Review':
      return { action: Actions.StartCropReview, target: ActionsTarget.CROP_REVIEW_QUEUE };
    case 'Crop Needed':
      return { action: Actions.StartCropNeeded, target: ActionsTarget.CROP_NEEDED_QUEUE };
    case 'Template Placement':
      return {
        action: Actions.StartTemplatePlacement,
        target: ActionsTarget.TEMPLATE_PLACEMENT_QUEUE,
      };
    case 'Ready For Artwork':
      return { action: Actions.StartArtworkReady, target: ActionsTarget.ARTWORK_READY_QUEUE };
    case 'Artwork Revision':
      return {
        action: Actions.StartArtworkRevision,
        target: ActionsTarget.ARTWORK_REVISION_QUEUE,
      };
    default:
      return null;
  }
};

/**
 * Get the Stop permission for a queue by name
 */
export const getStopPermissionForQueue = (queueName: string): QueuePermission | null => {
  switch (queueName) {
    case 'Crop Review':
      return { action: Actions.StopCropReview, target: ActionsTarget.CROP_REVIEW_QUEUE };
    case 'Crop Needed':
      return { action: Actions.StopCropNeeded, target: ActionsTarget.CROP_NEEDED_QUEUE };
    case 'Template Placement':
      return {
        action: Actions.StopTemplatePlacement,
        target: ActionsTarget.TEMPLATE_PLACEMENT_QUEUE,
      };
    case 'Ready For Artwork':
      return { action: Actions.StopArtworkReady, target: ActionsTarget.ARTWORK_READY_QUEUE };
    case 'Artwork Revision':
      return {
        action: Actions.StopArtworkRevision,
        target: ActionsTarget.ARTWORK_REVISION_QUEUE,
      };
    default:
      return null;
  }
};

/**
 * Get the View action for a queue type (for role form)
 */
export const getViewActionForQueue = (queueType: string): Actions | null => {
  switch (queueType) {
    case 'Crop Review Queue':
      return Actions.ViewCropReviewQueue;
    case 'Crop Needed Queue':
      return Actions.ViewCropNeededQueue;
    case 'Template Placement Queue':
      return Actions.ViewTemplatePlacementQueue;
    case 'Artwork Ready Queue':
      return Actions.ViewArtworkReadyQueue;
    case 'Artwork Revision Queue':
      return Actions.ViewArtworkRevisionQueue;
    default:
      return null;
  }
};

/**
 * Get the Start action for a queue type (for role form)
 */
export const getStartActionForQueue = (queueType: string): Actions | null => {
  switch (queueType) {
    case 'Crop Review Queue':
      return Actions.StartCropReview;
    case 'Crop Needed Queue':
      return Actions.StartCropNeeded;
    case 'Template Placement Queue':
      return Actions.StartTemplatePlacement;
    case 'Artwork Ready Queue':
      return Actions.StartArtworkReady;
    case 'Artwork Revision Queue':
      return Actions.StartArtworkRevision;
    default:
      return null;
  }
};

/**
 * Get the Stop action for a queue type (for role form)
 */
export const getStopActionForQueue = (queueType: string): Actions | null => {
  switch (queueType) {
    case 'Crop Review Queue':
      return Actions.StopCropReview;
    case 'Crop Needed Queue':
      return Actions.StopCropNeeded;
    case 'Template Placement Queue':
      return Actions.StopTemplatePlacement;
    case 'Artwork Ready Queue':
      return Actions.StopArtworkReady;
    case 'Artwork Revision Queue':
      return Actions.StopArtworkRevision;
    default:
      return null;
  }
};

/**
 * List of all queue types
 */
export const QUEUE_TYPES = [
  'Crop Review Queue',
  'Crop Needed Queue',
  'Template Placement Queue',
  'Artwork Ready Queue',
  'Artwork Revision Queue',
];
