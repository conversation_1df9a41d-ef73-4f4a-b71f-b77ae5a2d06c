import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import apiClient from '@/utils/axios';
import { defineAbilityFor } from '@/libs/casl/ability';
import { useAbility } from '@/libs/casl/AbilityContext';

const REFRESH_INTERVAL = 60000; // Check every minute

export const usePermissionRefresh = () => {
  const { data: session, update } = useSession();
  const ability = useAbility();
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  const refreshPermissions = async () => {
    try {
      // Only refresh if we have a session
      if (session?.user) {
        // Fetch the latest user data with roles
        const response = await apiClient.get('/auth/me');
        
        if (response.data && response.data.roles) {
          // Update the session with new roles
          await update({
            ...session,
            user: {
              ...session.user,
              roles: response.data.roles
            }
          });
          
          // Update ability context with new permissions
          const newAbility = defineAbilityFor({ roles: response.data.roles });
          Object.assign((ability as any), newAbility);
          
          setLastRefresh(new Date());
        }
      }
    } catch (error) {
      console.error('Failed to refresh permissions:', error);
    }
  };

  useEffect(() => {
    // Initial refresh
    refreshPermissions();
    
    // Set up interval for periodic refreshes
    const intervalId = setInterval(refreshPermissions, REFRESH_INTERVAL);
    
    // Clean up on unmount
    return () => clearInterval(intervalId);
  }, [session]);

  return { lastRefresh, refreshPermissions };
};