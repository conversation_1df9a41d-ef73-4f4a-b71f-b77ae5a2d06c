'use client';

import { useState, useEffect, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { useForm, Controller } from 'react-hook-form';
import Card from '@mui/material/Card';
import CardHeader from '@mui/material/CardHeader';
import CardContent from '@mui/material/CardContent';
import Grid from '@mui/material/Grid2';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';
import FormControl from '@mui/material/FormControl';
import InputLabel from '@mui/material/InputLabel';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import FormHelperText from '@mui/material/FormHelperText';
import Divider from '@mui/material/Divider';
import Typography from '@mui/material/Typography';
import Checkbox from '@mui/material/Checkbox';
import FormGroup from '@mui/material/FormGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import Switch from '@mui/material/Switch';
import apiClient from '@/utils/axios';
import { toast } from 'react-toastify';
import CircularProgress from '@mui/material/CircularProgress';
import ConfirmationDialog from '@/components/ConfirmationDialog';
import {
  resolvePermissionDependencies,
  isListingPagePermission,
  hasNonListingPagePermissions,
  mapResourceToTarget,
} from '@/utils/permissionUtils';
import { EnumPermissionData, PermissionData, RolesData } from '@/types/roleTypes';
import { Actions } from '@/libs/casl/ability';

const RoleAddForm = ({
  permissions,
  roleData,
  isEdit = false,
}: {
  permissions: PermissionData[];
  roleData?: RolesData;
  isEdit?: boolean;
}) => {
  console.log('permission data', permissions);
  const formKey = roleData?.id || 'new-role';
  const router = useRouter();
  const { data: session } = useSession();
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [openDialog, setOpenDialog] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors, isDirty },
    reset,
  } = useForm<RolesData>({
    defaultValues: roleData || {
      name: '',
      isActive: true,
      rolePermissions: [],
    },
  });

  const [selectedPermissions, setSelectedPermissions] = useState<Record<string, Actions[]>>({});
  const [selectAll, setSelectAll] = useState(false);
  const [permissionsDirty, setPermissionsDirty] = useState(false);
  const enumPermissions = useMemo(() => {
    const processedPermissions = permissions.map(permission => ({
      resource: permission.resource,
      actions: permission.actions.map(action => action as unknown as Actions),
    })) as EnumPermissionData[];

    const ordersPermission = processedPermissions.find(p => p.resource === 'Orders');
    const lineItemsPermission = processedPermissions.find(p => p.resource === 'Line Items');
    const filteredPermissions = processedPermissions.filter(p => p.resource !== 'Line Items');

    if (ordersPermission && lineItemsPermission) {
      return filteredPermissions.map(permission => {
        if (permission.resource === 'Orders') {
          return {
            ...permission,
            subResource: {
              name: 'Line Items',
              actions: lineItemsPermission.actions,
            },
          };
        }
        return permission;
      });
    }

    return processedPermissions;
  }, [permissions]);

  // Initialize permissions if editing
  useEffect(() => {
    if (!isEdit || !roleData) {
      setInitialLoading(false);
      return;
    }

    const permMap: Record<string, Actions[]> = {};

    if (roleData.rolePermissions && Array.isArray(roleData.rolePermissions)) {
      roleData.rolePermissions.forEach(perm => {
        if (perm.resource && Array.isArray(perm.actions)) {
          permMap[perm.resource] = perm.actions.map(action => action as unknown as Actions);
        }
      });
    }

    // Calculate if all permissions are selected
    let totalAvailablePermissions = 0;
    let totalSelectedPermissions = 0;

    enumPermissions.forEach(permission => {
      totalAvailablePermissions += permission.actions.length;
      const selectedActions = permMap[permission.resource] || [];
      totalSelectedPermissions += selectedActions.length;
    });

    const allSelected =
      totalAvailablePermissions > 0 && totalSelectedPermissions === totalAvailablePermissions;

    // Batch state updates
    setSelectedPermissions(permMap);
    setSelectAll(allSelected);
    setInitialLoading(false);
  }, [isEdit, roleData, enumPermissions]);

  const handlePermissionChange = (resource: string, action: string, checked: boolean) => {
    setPermissionsDirty(true);

    if (resource === 'Orders') {
      if (action === Actions.EditOrderDetialPage && checked) {
        // When EditOrderDetialPage is checked, also check ViewOrderListingPage
        setSelectedPermissions(prev => {
          const ordersPermissions = prev['Orders'] || [];
          return {
            ...prev,
            Orders: [
              ...new Set([
                ...ordersPermissions,
                Actions.EditOrderDetialPage,
                Actions.ViewOrderListingPage,
              ]),
            ],
          };
        });
        return;
      } else if (action === Actions.ViewOrderListingPage && !checked) {
        // When ViewOrderListingPage is unchecked, also uncheck EditOrderDetialPage
        // unless there are Line Items selected
        setSelectedPermissions(prev => {
          if ((prev['Line Items'] || []).length > 0) {
            return prev; // Don't uncheck if Line Items are selected
          }

          const ordersPermissions = prev['Orders'] || [];
          return {
            ...prev,
            Orders: ordersPermissions.filter(
              a => a !== Actions.ViewOrderListingPage && a !== Actions.EditOrderDetialPage,
            ),
          };
        });
        return;
      } else if (
        (action === Actions.ViewOrderListingPage || action === Actions.EditOrderDetialPage) &&
        !checked &&
        (selectedPermissions['Line Items'] || []).length > 0
      ) {
        // Don't allow unchecking if Line Items are selected
        return;
      }

      // Handle normal Orders permission changes
      setSelectedPermissions(prev => {
        const ordersPermissions = prev['Orders'] || [];
        return {
          ...prev,
          Orders: checked
            ? [...ordersPermissions, action as unknown as Actions]
            : ordersPermissions.filter(a => a !== action),
        };
      });
      return;
    }

    if (resource === 'Line Items') {
      setSelectedPermissions(prev => {
        const lineItemsPermissions = prev['Line Items'] || [];
        const newLineItemsPermissions = checked
          ? [...lineItemsPermissions, action as unknown as Actions]
          : lineItemsPermissions.filter(a => a !== action);

        if (checked) {
          const ordersPermissions = prev['Orders'] || [];
          return {
            ...prev,
            'Line Items': newLineItemsPermissions,
            Orders:
              ordersPermissions.includes(Actions.ViewOrderListingPage) &&
              ordersPermissions.includes(Actions.EditOrderDetialPage)
                ? ordersPermissions
                : [
                    ...ordersPermissions,
                    Actions.ViewOrderListingPage,
                    Actions.EditOrderDetialPage,
                  ].filter((v, i, a) => a.indexOf(v) === i),
          };
        }

        if (newLineItemsPermissions.length === 0) {
          return {
            ...prev,
            'Line Items': [],
          };
        }

        return {
          ...prev,
          'Line Items': newLineItemsPermissions,
        };
      });
      return;
    }

    if (resource === 'Product Information Management System') {
      setSelectedPermissions(prev => {
        let newState = { ...prev };
        const current = prev[resource] || [];

        if (checked) {
          newState = {
            ...newState,
            [resource]: [...new Set([...current, action as unknown as Actions])],
          };
        } else {
          // Prevent unchecking ViewPIMSListingPage if AddArtwork is selected
          if (action === Actions.ViewPIMSListingPage && current.includes(Actions.AddArtwork)) {
            return prev; // Do not allow unchecking
          }
          newState = {
            ...newState,
            [resource]: current.filter(a => a !== action),
          };
        }

        // Resolve dependencies (this will add EditDetailPage, ViewDetailPage, and ViewPIMSListingPage for AddArtwork)
        newState = resolvePermissionDependencies(newState);

        return newState;
      });
      return;
    }

    setSelectedPermissions(prev => {
      let newState = { ...prev };

      if (checked) {
        const current = prev[resource] || [];
        newState = {
          ...newState,
          [resource]: [...new Set([...current, action as unknown as Actions])],
        };
      } else {
        const current = prev[resource] || [];

        if (
          isListingPagePermission(action as unknown as Actions) &&
          hasNonListingPagePermissions(mapResourceToTarget(resource), current)
        ) {
          return prev;
        }

        newState = {
          ...newState,
          [resource]: current.filter(a => a !== action),
        };
        setSelectAll(false);
      }

      newState = resolvePermissionDependencies(newState);

      return newState;
    });
  };

  const isCheckboxDisabled = (resource: string, action: Actions): boolean => {
    if (resource === 'Orders') {
      if (action === Actions.ViewOrderListingPage) {
        // Disable ViewOrderListingPage if EditOrderDetialPage is selected or Line Items exist
        const hasLineItems = (selectedPermissions['Line Items'] || []).length > 0;
        const hasEditOrderDetail =
          selectedPermissions['Orders']?.includes(Actions.EditOrderDetialPage) || false;
        return hasLineItems || hasEditOrderDetail;
      }

      if (action === Actions.EditOrderDetialPage) {
        // Disable EditOrderDetialPage if Line Items exist
        const hasLineItems = (selectedPermissions['Line Items'] || []).length > 0;
        return hasLineItems;
      }
    }

    // For PIMS resource, disable ViewPIMSListingPage if AddArtwork is selected
    if (resource === 'Product Information Management System') {
      if (action === Actions.ViewPIMSListingPage) {
        const hasAddArtwork = selectedPermissions[resource]?.includes(Actions.AddArtwork) || false;
        return hasAddArtwork;
      }
    }

    // For other resources, use the standard logic
    if (resource !== 'Orders' && isListingPagePermission(action)) {
      const isSelected = selectedPermissions[resource]?.includes(action) || false;
      const hasOtherPermissions = hasNonListingPagePermissions(
        mapResourceToTarget(resource),
        selectedPermissions[resource] || [],
      );
      return isSelected && hasOtherPermissions;
    }

    return false;
  };

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    const checked = event.target.checked;
    setSelectAll(checked);
    setPermissionsDirty(true);

    if (checked) {
      // Select all permissions including subResources
      const allPermissions: Record<string, Actions[]> = {};

      enumPermissions.forEach(permission => {
        // Add main resource permissions
        allPermissions[permission.resource] = permission.actions.map(
          (action: any) => action as unknown as Actions,
        );

        // Add subResource permissions if they exist
        if (permission.subResource) {
          allPermissions[permission.subResource.name] = permission.subResource.actions.map(
            (action: any) => action as unknown as Actions,
          );
        }
      });

      setSelectedPermissions(allPermissions);
    } else {
      // Deselect all permissions
      setSelectedPermissions({});
    }
  };

  const onSubmit = async (data: RolesData) => {
    if (!session?.user?.token) {
      toast.error('Authentication required');
      return;
    }

    // Final check to ensure ViewOrderListingPage and EditOrderDetialPage are included if Line Items exist
    let finalPermissions = { ...selectedPermissions };
    const hasLineItems = (finalPermissions['Line Items'] || []).length > 0;

    if (hasLineItems) {
      // Ensure ViewOrderListingPage and EditOrderDetialPage are selected when Line Items exist
      const ordersPermissions = finalPermissions['Orders'] || [];
      const requiredPermissions = [Actions.ViewOrderListingPage, Actions.EditOrderDetialPage];
      const missingPermissions = requiredPermissions.filter(p => !ordersPermissions.includes(p));

      if (missingPermissions.length > 0) {
        finalPermissions = {
          ...finalPermissions,
          Orders: [...ordersPermissions, ...missingPermissions],
        };
      }
    }

    const formattedPermissions = Object.entries(finalPermissions)
      .map(([resource, actions]) => ({
        resource,
        // Convert Actions enum values back to strings for API
        actions: actions.map(action => action.toString()),
      }))
      .filter(p => p.actions.length > 0);

    const roleData = {
      ...data,
      rolePermissions: formattedPermissions,
    };

    setLoading(true);

    try {
      if (isEdit) {
        await apiClient.patch(`/roles/${roleData?.id}`, roleData);
        toast.success('Role updated successfully');
      } else {
        await apiClient.post('/roles', roleData);
        toast.success('Role created successfully');
      }
      router.push('/roles');
    } catch (error: any) {
      const errorMessage = getErrorMessage(error);
      toast.error(`Failed to ${isEdit ? 'update' : 'create'} role: ${errorMessage}`);

      if (error?.response?.status !== 400) {
        console.error(`Failed to ${isEdit ? 'update' : 'create'} role:`, error);
      }
    } finally {
      setLoading(false);
    }
  };

  // Helper function to extract error message
  const getErrorMessage = (error: any): string => {
    if (error.response) {
      if (Array.isArray(error.response.data?.message)) {
        return error.response.data.message[0];
      } else if (typeof error.response.data?.message === 'string') {
        return error.response.data.message;
      } else if (typeof error.response.data === 'string') {
        return error.response.data;
      }
    } else if (error.message) {
      return error.message;
    }
    return 'Something went wrong. Please try again.';
  };

  const handleCancel = () => {
    if (isDirty || permissionsDirty) {
      setOpenDialog(true);
    } else {
      router.push('/roles');
    }
  };

  const handleDialogConfirm = () => {
    setOpenDialog(false);
    router.push('/roles');
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      setInitialLoading(false);
    }, 300);
    return () => clearTimeout(timer);
  }, []);

  if (initialLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <CircularProgress />
      </div>
    );
  }

  return (
    <>
      <form key={formKey} onSubmit={handleSubmit(onSubmit)}>
        <Card>
          <div className="flex justify-between items-center px-6 pt-6">
            <CardHeader title={isEdit ? 'Edit Role' : 'Add New Role'} className="p-0" />
            <FormControlLabel
              control={<Switch checked={selectAll} onChange={handleSelectAll} color="primary" />}
              label="Select All Permissions"
              labelPlacement="start"
            />
          </div>
          <CardContent>
            <Grid container spacing={5}>
              <Grid size={{ xs: 12, md: 6 }}>
                <Controller
                  name="name"
                  control={control}
                  rules={{ required: 'Role name is required' }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Role Name"
                      placeholder="Enter role name"
                      error={Boolean(errors.name)}
                      helperText={errors.name?.message}
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, md: 6 }}>
                <Controller
                  name="isActive"
                  control={control}
                  render={({ field }) => (
                    <FormControl fullWidth error={Boolean(errors.isActive)}>
                      <InputLabel id="role-status-label">Status</InputLabel>
                      <Select {...field} labelId="role-status-label" label="Status">
                        <MenuItem value={true as any}>Active</MenuItem>
                        <MenuItem value={false as any}>Inactive</MenuItem>
                      </Select>
                      {errors.isActive && (
                        <FormHelperText>{errors.isActive.message}</FormHelperText>
                      )}
                    </FormControl>
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12 }}>
                <Divider />
                <Typography variant="h6" className="mbs-4 mbe-2">
                  Role Permissions
                </Typography>

                {enumPermissions?.map(permission => (
                  <Card key={permission.resource} className="mbe-4" variant="outlined">
                    <CardHeader title={permission.resource} />
                    <CardContent>
                      <FormGroup row>
                        {permission.actions.map((action: any) => {
                          // Convert action to Actions enum type
                          const actionEnum = action as unknown as Actions;

                          // Check if the permission is selected
                          const isSelected =
                            selectedPermissions[permission.resource]?.includes(actionEnum) || false;

                          // Determine if checkbox should be disabled
                          const isDisabled = isCheckboxDisabled(permission.resource, actionEnum);

                          return (
                            <FormControlLabel
                              key={`${permission.resource}-${action}`}
                              control={
                                <Checkbox
                                  checked={isSelected}
                                  onChange={(e, checked) =>
                                    handlePermissionChange(permission.resource, action, checked)
                                  }
                                  disabled={isDisabled}
                                />
                              }
                              label={action}
                            />
                          );
                        })}
                      </FormGroup>

                      {/* Render sub-resource if it exists */}
                      {permission.subResource && (
                        <div className="mt-4 pt-2 pb-2 pl-4 pr-4 rounded border border-gray-200">
                          <Typography variant="subtitle2" className="mb-2">
                            Line Items
                          </Typography>
                          {permission.subResource.actions.map((action: any) => {
                            const isSelected =
                              selectedPermissions['Line Items']?.includes(
                                action as unknown as Actions,
                              ) || false;

                            return (
                              <FormControlLabel
                                key={`Line Items-${action}`}
                                control={
                                  <Checkbox
                                    checked={isSelected}
                                    onChange={(e, checked) =>
                                      handlePermissionChange('Line Items', action, checked)
                                    }
                                  />
                                }
                                label={action}
                              />
                            );
                          })}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </Grid>

              <Grid size={{ xs: 12 }} className="flex justify-end gap-4">
                <Button variant="outlined" onClick={handleCancel}>
                  Cancel
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  disabled={loading || (!isDirty && !permissionsDirty)}
                >
                  {loading ? (
                    <>
                      <CircularProgress size={20} color="inherit" className="mie-2" />
                      Saving...
                    </>
                  ) : (
                    'Save Role'
                  )}
                </Button>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </form>

      <ConfirmationDialog
        open={openDialog}
        title="Unsaved Changes"
        message="You have unsaved changes. Are you sure you want to leave without saving?"
        confirmLabel="Leave"
        confirmColor="primary"
        onConfirm={handleDialogConfirm}
        onCancel={() => setOpenDialog(false)}
      />
    </>
  );
};

export default RoleAddForm;
