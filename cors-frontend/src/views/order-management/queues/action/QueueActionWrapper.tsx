'use client';
import { <PERSON><PERSON>, <PERSON>, But<PERSON>, Card, CardContent, Divider, Typography } from '@mui/material';
import ProductAttributesViewer from '../components/ProductAttributesViewer';
import CropNeededQueue from './sub-components/CropNeededQueue';
import CropReviewQueue from './sub-components/CropReviewQueue';
import TemplatePlacementQueue from './sub-components/TemplatePlacementQueue';
import ArtworkQueue from './sub-components/ArtworkQueue';
import LoadingView from '@/components/LoadingView';
import { SingleQueueItem } from '@/types/queues.types';
import useApi from '@/hooks/useApiCall';
import { useRouter } from 'next/navigation';

const QueueActionWrapper = ({ actionType, queueId }: { actionType: string; queueId: string }) => {
  const router = useRouter();
  const { data: queueItem, isLoading } = useApi<SingleQueueItem>(
    `/workflow-queues/get-queue-item/`,
    'get',
    true,
    { queryParams: { queueId } },
  );
  const {
    isLoading: isLoadingEndAction,
    makeRequest: requestEndAction,
    isSuccess: isSuccessEndAction,
  } = useApi<SingleQueueItem>(`/workflow-queues/stop-review/`, 'get', false, {
    queryParams: { queueId },
  });

  if (isSuccessEndAction) {
    router.push('/ordermanagement/queues');
  }
  if (isLoading) return <LoadingView />;
  return (
    <Card>
      <CardContent>
        <Typography variant="h5" sx={{ mb: 4 }}>
          {actionType
            .split('-')
            .map(word => word?.charAt(0)?.toUpperCase() + word?.slice(1))
            .join(' ')}
        </Typography>
        <Box
          sx={{
            display: 'flex',
            gap: 2,
            justifyContent: 'space-between',
            alignItems: 'center',
            my: 4,
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Avatar>
              {queueItem?.attachments?.assigned_to?.charAt(0) ||
                queueItem?.lineItems?.assigned_to?.charAt(0)}
            </Avatar>
            <Box>
              <Typography variant="h6">
                {queueItem?.attachments?.assigned_to || queueItem?.lineItems?.assigned_to}
              </Typography>
            </Box>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Button
              color="error"
              variant="outlined"
              onClick={async () => {
                if (!queueId) return;
                requestEndAction();
              }}
              disabled={isLoading || isLoadingEndAction}
              sx={{ width: '100px' }}
            >
              {isLoadingEndAction ? 'Stopping...' : 'Stop'}
            </Button>
          </Box>
        </Box>
        <Divider sx={{ my: 4 }} />
        <ProductAttributesViewer actionType={actionType} queueItem={queueItem} />
        <Divider sx={{ my: 4 }} />
        {actionType === 'Crop Needed' && <CropNeededQueue />}
        {actionType === 'Crop Review' && (
          <CropReviewQueue queueItem={queueItem} queueId={queueId} />
        )}
        {actionType === 'Template Placement' && <TemplatePlacementQueue />}
        {actionType === 'Ready For Artwork' && <ArtworkQueue actionType={actionType} />}
        {actionType === 'Artwork Revision' && <ArtworkQueue actionType={actionType} />}
      </CardContent>
    </Card>
  );
};

export default QueueActionWrapper;
