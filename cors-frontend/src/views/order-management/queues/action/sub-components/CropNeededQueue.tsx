'use client';
import Button from '@/components/Button';
import { Box } from '@mui/material';
import Grid from '@mui/material/Grid2';
import SingleImageViewCard from '@/components/card-statistics/SingleImageViewCard';
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import ImageUploadField from '@/@core/components/mui/ImageUpload';

type CropReviewFormType = {
  image: string[];
};
const CropNeededQueue = () => {
  const {
    handleSubmit,
    control,
    setValue,

    formState: { errors, isSubmitting },
  } = useForm<CropReviewFormType>({
    resolver: yupResolver(
      yup.object().shape({
        image: yup
          .array()
          .of(yup.string().required())
          .min(1, 'At least one image is required')
          .max(1, 'Only one image is allowed')
          .required('Image is required'),
      }),
    ),
    defaultValues: {
      image: [],
    },
  });
  const onSubmit = async (data: CropReviewFormType) => {
    // console.log('datadata', data);
  };
  return (
    <>
      {/* Images Section */}
      <Grid container spacing={4} sx={{ mb: 4 }}>
        <Grid size={{ xs: 12, md: 6 }}>
          <SingleImageViewCard
            imageUrl="/images/avatars/1.png"
            title="Customer Image"
            downloadUrl="/images/avatars/1.png"
          />
        </Grid>

        <Grid size={{ xs: 12, md: 6 }}>
          <SingleImageViewCard
            imageUrl="/images/avatars/1.png"
            title="Denied Image"
            downloadUrl="/images/avatars/1.png"
            isDanger={true}
          />
        </Grid>
      </Grid>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Controller
          name={`image`}
          control={control}
          rules={{ required: 'Image is required' }}
          render={({ field: { onChange, value }, fieldState: { error } }) => (
            <ImageUploadField
              control={control}
              errors={errors}
              setValue={setValue}
              name={`image`}
              minImages={1}
              maxImages={1}
              maxFileSizeMB={5}
              formValue={value}
              errorField={error?.message}
            />
          )}
        />
        {/* Actions Section */}
        <Box maxWidth="600px" mx="auto">
          <Grid container spacing={2} justifyContent="center">
            <Grid size={{ xs: 12, sm: 4 }}>
              <Button
                variant="outlined"
                fullWidth
                size="small"
                title="Submit"
                type="submit"
                disabled={isSubmitting}
              />
            </Grid>
          </Grid>
        </Box>
      </form>
    </>
  );
};

export default CropNeededQueue;
