'use client';
import Button from '@/components/Button';
import { Box, Chip, TextField, Typography } from '@mui/material';
import Grid from '@mui/material/Grid2';
import SingleImageViewCard from '@/components/card-statistics/SingleImageViewCard';
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import ImageUploadField from '@/@core/components/mui/ImageUpload';
import { useState } from 'react';
type FormType = {
  image: string[];
  request: string;
};
const ArtworkQueue = ({ actionType }: { actionType: string }) => {
  const [showAddRequest, setShowAddRequest] = useState(false);
  const {
    handleSubmit,
    control,
    setValue,
    register,
    formState: { errors, isSubmitting },
  } = useForm<FormType>({
    resolver: yupResolver(
      yup.object().shape({
        image: yup
          .array()
          .of(yup.string().required())
          .min(1, 'At least one art file is required')
          .max(1, 'Only one art file is allowed')
          .required('Art file is required'),
        request: yup.string().required('Request is required'),
      }),
    ),
    defaultValues: {
      image: [],
    },
  });
  const onSubmit = async (data: FormType) => {
    // console.log('datadata', data);
  };
  return (
    <>
      {/* Images Section */}
      <Grid container spacing={4} sx={{ mb: 4 }}>
        {Array.from({ length: 3 }).map((_, index) => (
          <Grid key={index} size={{ xs: 12, md: Array.from({ length: 3 }).length >= 3 ? 4 : 6 }}>
            <SingleImageViewCard
              imageUrl="/images/avatars/1.png"
              title={`Pet ${index + 1}`}
              downloadUrl="/images/avatars/1.png"
            />
          </Grid>
        ))}
      </Grid>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Controller
          name={`image`}
          control={control}
          rules={{ required: 'Image is required' }}
          render={({ field: { onChange, value }, fieldState: { error } }) => (
            <ImageUploadField
              control={control}
              errors={errors}
              setValue={setValue}
              name={`image`}
              minImages={1}
              maxImages={1}
              maxFileSizeMB={5}
              formValue={value}
              errorField={error?.message}
              title="Upload Art File"
              buttonText="Upload"
            />
          )}
        />
        <Button
          variant="outlined"
          fullWidth
          size="small"
          title="Add Request"
          type="button"
          sx={{ width: '200px', mb: 4 }}
          onClick={() => setShowAddRequest(!showAddRequest)}
        />
        {showAddRequest && (
          <Box>
            <TextField
              fullWidth
              label="Request"
              {...register('request')}
              error={!!errors.request}
              helperText={errors.request?.message}
              sx={{ maxWidth: '50%', mb: 4 }}
            />
          </Box>
        )}

        <Box>
          <Typography variant="h6">Requests</Typography>
          <Box sx={{ display: 'flex', flexDirection: 'row', gap: 2 }}>
            <Typography variant="body1">NOV 25,2025</Typography>
            <Typography variant="body1">10:15:34 AM</Typography>
            <Typography variant="body1">Artist Name</Typography>
          </Box>
          <Chip
            label={'abc.jpg'}
            size="small"
            variant="outlined"
            style={{ marginRight: 4, marginTop: 4 }}
          />
        </Box>
        {/* Actions Section */}
        <Box maxWidth="600px" mx="auto">
          <Grid container spacing={2} justifyContent="center">
            <Grid size={{ xs: 12, sm: 4 }}>
              <Button
                variant="outlined"
                fullWidth
                size="small"
                title="Submit"
                type="submit"
                disabled={isSubmitting}
              />
            </Grid>
          </Grid>
        </Box>
      </form>
    </>
  );
};

export default ArtworkQueue;
