'use client';
import React, { useEffect, useState } from 'react';
import { useTableColumns } from '../../../../hooks/useTableColumns';
import Link from 'next/link';
import { SingleQueueListData, SingleQueueRow, TabQueues } from '@/types/queues.types';
import { CropTypeEnum } from '@/constants/queue.constants';
import QueuesTable from './QueuesTable';
import useApi from '@/hooks/useApiCall';
import { Box, Button } from '@mui/material';
import { toast } from 'react-toastify';
import { useRouter } from 'next/navigation';
import { useAbility } from '@/libs/casl/AbilityContext';
import { Actions, ActionsTarget } from '@/libs/casl/ability';

const QueuesTableWrapper = ({ queue }: { queue: TabQueues }) => {
  const router = useRouter();
  const ability = useAbility();
  const [page, setPage] = useState(0);
  const [limit, setLimit] = useState(25);

  // Helper function to get the Start permission for a queue
  const getStartPermissionForQueue = (
    queueName: string,
  ): { action: Actions; target: ActionsTarget } | null => {
    switch (queueName) {
      case 'Crop Review':
        return { action: Actions.StartCropReview, target: ActionsTarget.CROP_REVIEW_QUEUE };
      case 'Crop Needed':
        return { action: Actions.StartCropNeeded, target: ActionsTarget.CROP_NEEDED_QUEUE };
      case 'Template Placement':
        return {
          action: Actions.StartTemplatePlacement,
          target: ActionsTarget.TEMPLATE_PLACEMENT_QUEUE,
        };
      case 'Ready For Artwork':
        return { action: Actions.StartArtworkReady, target: ActionsTarget.ARTWORK_READY_QUEUE };
      case 'Artwork Revision':
        return {
          action: Actions.StartArtworkRevision,
          target: ActionsTarget.ARTWORK_REVISION_QUEUE,
        };
      default:
        return null;
    }
  };

  // Check if user has Start permission for this queue
  const hasStartPermission = () => {
    const permission = getStartPermissionForQueue(queue.name);
    if (!permission) return false;
    return ability?.can(permission.action, permission.target);
  };
  const { isLoading: isLoadingAssign, makeRequest: requestAssign } = useApi(
    `/workflow-queues/assign/`,
    'get',
  );
  const handleStart = async () => {
    if (!queue) return;
    const data = await requestAssign({ queryParams: { queueId: queue?.id } });

    if (data?.total_assigned) {
      router.push(`/ordermanagement/queues/action/${queue.name}?queueId=${queue?.id}`);
    } else {
      toast.error('No items to assign');
    }
  };
  const {
    data,
    isLoading,
    makeRequest: requestQueues,
  } = useApi<SingleQueueListData>(`/workflow-queues`, 'get', true, {
    queryParams: {
      id: queue?.id,
      page: page + 1,
      limit: limit,
    },
  });

  useEffect(() => {
    requestQueues({
      queryParams: {
        id: queue?.id,
        page: page + 1,
        limit: limit,
      },
    });
  }, [queue, page, limit]);

  const columns = useTableColumns<SingleQueueRow>([
    {
      accessor: (row: SingleQueueRow) => row.priority || 'Standard',
      header: 'Priority',
      type: 'chip',
      chipConfig: {
        valueMap: {
          Rush: { label: 'Rush', color: 'error' },
          Standard: { label: 'Standard', color: 'warning' },
          '-': { label: '-', color: 'default' },
        },
      },
    },
    ...(queue?.name !== 'Crop Needed' && queue?.name !== 'Crop Review'
      ? [
          {
            accessor: (row: SingleQueueRow) => row.sku || '-',
            header: 'Item SKU',
            type: 'text' as const,
          },
        ]
      : []),

    ...(queue?.name === 'Crop Needed' || queue?.name === 'Crop Review'
      ? [
          {
            accessor: (row: SingleQueueRow) => row.cutout_pro_url?.split('/').pop() || '-',
            header: 'File Name',
            type: 'link' as const,
            linkConfig: {
              href: (value: any, row: SingleQueueRow) => row.cutout_pro_url || '',
              target: '_blank' as const,
            },
          },
        ]
      : [
          {
            accessor: (row: SingleQueueRow) => row.attachments || '-',
            header: 'File Name(s)',
            type: 'custom' as const,
            customRender: (value: any, row: SingleQueueRow) => {
              return (
                <div>
                  {row.attachments?.map(attachment => (
                    <Link
                      href={attachment?.cutout_pro_url || ''}
                      target={'_blank'}
                      rel={'noopener noreferrer'}
                    >
                      {attachment?.cutout_pro_url?.split('/').pop() || '-'}
                    </Link>
                  ))}
                </div>
              );
            },
          },
        ]),

    ...(queue?.name === 'Crop Needed' || queue?.name === 'Crop Review'
      ? [
          {
            accessor: (row: SingleQueueRow) =>
              CropTypeEnum[row.cropType as keyof typeof CropTypeEnum] || '-',
            header: 'Crop Type',
            type: 'text' as const,
          },
        ]
      : []),

    {
      accessor: (row: SingleQueueRow) => row.order_number || '-',
      header: 'Order #',
      type: 'text',
    },
    {
      accessor: (row: SingleQueueRow) => row.order_date || '-',
      header: 'Order Date',
      type: 'date',
    },
    ...(queue?.name !== 'Crop Needed' && queue?.name !== 'Crop Review'
      ? [
          {
            accessor: (row: SingleQueueRow) => row.line_item_status || '-',
            header: 'Item Status',
            type: 'chip' as const,
          },
        ]
      : []),

    {
      accessor: (row: SingleQueueRow) => row.assigned_to || '-',
      header: 'Assigned To',
      type: 'text',
    },
  ]);
  return (
    <>
      {' '}
      {hasStartPermission() && (
        <Box sx={{ display: 'flex', justifyContent: 'end', mb: 2 }}>
          <Button
            variant="contained"
            color="primary"
            onClick={() => handleStart()}
            disabled={isLoadingAssign}
          >
            {isLoadingAssign ? `Starting...` : `Start ${queue.name}`}
          </Button>
        </Box>
      )}
      <QueuesTable
        data={
          data ? (data?.attachments?.length > 0 ? data?.attachments : data?.lineItems || []) : []
        }
        columns={columns || []}
        loading={isLoading}
        queue={queue}
        page={page}
        limit={limit}
        setPage={setPage}
        setLimit={setLimit}
      />
    </>
  );
};

export default QueuesTableWrapper;
