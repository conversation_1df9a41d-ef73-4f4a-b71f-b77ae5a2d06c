import { useState, useEffect } from 'react';
import apiClient from '@/utils/axios';
import { toast } from 'react-toastify';
import {
  Typography,
  Chip,
  Box,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
  Button,
  IconButton,
  Tooltip,
  DialogContentText,
} from '@mui/material';
import Grid2 from '@mui/material/Grid2';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import EditIcon from '@mui/icons-material/Edit';
import CancelIcon from '@mui/icons-material/Cancel';
import RestartAltIcon from '@mui/icons-material/RestartAlt';
import FlagIcon from '@mui/icons-material/Flag';
import ContactSupportIcon from '@mui/icons-material/ContactSupport';
import ConfirmationDialog from '@/components/ConfirmationDialog';
import EditItemDialog from '../dialogs/EditItemDialog';
import CancelItemDialog from '../dialogs/CancelItemDialog';
import RemakeItemDialog from '../dialogs/RemakeItemDialog';
import FlagItemDialog from '../dialogs/FlagItemDialog';
import ArtfileSection from '../components/ArtfileSection';
import CustomerImagesSection from '../components/CustomerImagesSection';
import ArtworkRequestsSection from '../components/ArtworkRequestsSection';
import CustomerContactDialog from '../dialogs/CustomerContactDialog';
import StatusChip from '@/components/StatusChip';
import {
  canCancelLineItem,
  canCreateRemake,
  canEditLineItem,
  canFlagLineItem,
  canInitiateCustomerContact,
  isItemActionable,
} from '@/utils/ordermoduleUtils';
import { OrderDetailsTabProps } from '@/types/orderDetailsTab.types';

const OrderDetailsTab = ({ orderData }: OrderDetailsTabProps) => {
  const [expanded, setExpanded] = useState<string | false>(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [cancelDialogOpen, setCancelDialogOpen] = useState(false);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [remakeDialogOpen, setRemakeDialogOpen] = useState(false);
  const [flagDialogOpen, setFlagDialogOpen] = useState(false);
  const [currentEditItem, setCurrentEditItem] = useState<any>(null);
  const [artworkRequests, setArtworkRequests] = useState<any[]>([]);
  const [contactDialogOpen, setContactDialogOpen] = useState(false);
  const [currentContactItem, setCurrentContactItem] = useState<any>(null);
  const [isUpdating, setIsUpdating] = useState(false);
  const [updatedOrderData, setUpdatedOrderData] = useState(orderData);

  const isItemEditable = (item: OrderDetailsTabProps['orderData']['lineItems'][0]) =>
    isItemActionable(item) && canEditLineItem();
  const isItemCancellable = (item: OrderDetailsTabProps['orderData']['lineItems'][0]) =>
    isItemActionable(item, true) && canCancelLineItem();
  const isItemRemakeable = (item: OrderDetailsTabProps['orderData']['lineItems'][0]) =>
    isItemActionable(item) && canCreateRemake();

  useEffect(() => {
    setUpdatedOrderData(orderData);
  }, [orderData]);

  // Function to fetch updated order data
  const fetchOrderData = async () => {
    try {
      const response = await apiClient.get(`/orders/${orderData.id}`);
      setUpdatedOrderData(response.data);
    } catch (error) {
      toast.error('Failed to refresh order data.');
    }
  };

  const handleApiCall = async (apiFunction: () => Promise<any>, successMessage: string) => {
    setIsUpdating(true);
    try {
      await apiFunction();
      toast.success(successMessage);
      await fetchOrderData();
    } catch (error) {
      toast.error('An error occurred. Please try again.');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>, itemId: string) => {
    if (event.target.files && event.target.files.length > 0) {
      const file = event.target.files[0];

      const uploadData = {
        itemId,
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type,
        uploadedBy: 'Current User',
        uploadedAt: new Date().toISOString(),
      };

      event.target.value = '';
    }
  };

  const handleChange = (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpanded(isExpanded ? panel : false);
  };

  const handleEditClick = (item: any) => {
    setCurrentEditItem(item);
    setEditDialogOpen(true);
  };

  const handleCancelClick = (item: any) => {
    setCurrentEditItem(item);
    setCancelDialogOpen(true);
  };

  const handleRemakeClick = (item: any) => {
    setCurrentEditItem(item);
    setRemakeDialogOpen(true);
  };

  const handleFlagClick = (item: any) => {
    setCurrentEditItem(item);
    setFlagDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setEditDialogOpen(false);
    setCancelDialogOpen(false);
    setRemakeDialogOpen(false);
    setFlagDialogOpen(false);
    setCurrentEditItem(null);
  };

  const handleSaveItem = async (
    productSkuId: string,
    quantity: number,
    images?: File[],
    imageUrls?: string[],
  ) => {
    if (!currentEditItem) return;

    setIsUpdating(true);
    try {
      // Prepare metadata with image URLs if available
      const metadata: Record<string, any> = {
        ...currentEditItem.metadata,
      };

      // Add image URLs to metadata if provided
      if (imageUrls && imageUrls.length > 0) {
        imageUrls.forEach((url, index) => {
          metadata[`_image_url_${index + 1}`] = url;
        });
      }

      // Update the line item with new SKU, quantity and metadata
      await apiClient.put(`orders/line-items/${currentEditItem.id}`, {
        productSkuId,
        quantity,
        metadata,
      });

      toast.success('Item updated successfully');
      await fetchOrderData();
    } catch (error) {
      toast.error('Failed to update item. Please try again.');
    } finally {
      setIsUpdating(false);
      handleCloseDialog();
    }
  };

  const handleCancelItem = (reason: string) => {
    if (!reason || !currentEditItem) return;

    handleApiCall(async () => {
      await apiClient.put(`orders/line-items/${currentEditItem.id}`, {
        status: 'cancelled',
        cancelReason: reason,
      });
    }, 'Item cancelled successfully');

    handleCloseDialog();
  };

  const handleFlagItem = async (flagReason: string, flagged: boolean) => {
    if (!currentEditItem) return;

    const payload = {
      flagged: flagged,
      ...(flagged ? { flagReason: flagReason } : {}),
    };

    setIsUpdating(true);
    try {
      await apiClient.put(`orders/line-items/${currentEditItem.id}`, payload);
      setUpdatedOrderData((prevData: any) => {
        const newData = { ...prevData };
        if (newData.lineItems) {
          newData.lineItems = newData.lineItems.map((item: any) => {
            if (item.id === currentEditItem.id) {
              return {
                ...item,
                flagged: flagged,
                flagReason: flagged ? flagReason : null,
              };
            }
            return item;
          });
        }

        return newData;
      });

      toast.success(flagged ? 'Item flagged successfully' : 'Item unflagged successfully');
      await fetchOrderData();
    } catch (error) {
      toast.error('An error occurred. Please try again.');
    } finally {
      setIsUpdating(false);
      handleCloseDialog();
    }
  };

  const handleConfirmAction = () => {
    setConfirmDialogOpen(false);
  };

  const handleSubmitRemake = (data: {
    remakeReasons: string[];
    detailedReasons: string[];
    sku: string;
    images: File[];
    imageUrls?: string[];
  }) => {
    if (!currentEditItem) return;
    handleApiCall(async () => {
      const metadata: Record<string, any> = {};
      if (data.imageUrls && data.imageUrls.length > 0) {
        data.imageUrls.forEach((url, index) => {
          metadata[`image_url_${index + 1}`] = url;
        });
      }
      await apiClient.post(`/orders/line-items/remake`, {
        productSkuId: data.sku,
        lineItemId: currentEditItem.id,
        remakeReason: data.remakeReasons,
        detailedRemakeReason: data.detailedReasons,
        metadata,
      });
    }, 'Remake created successfully');

    handleCloseDialog();
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const handleAddArtworkRequest = (message: string, photos: File[]) => {
    const newRequest = {
      id: `request-${Date.now()}`,
      message,
      createdAt: new Date().toISOString(),
      createdBy: {
        id: 'current-user',
        name: 'Current User',
        avatar: '',
      },
      photos: photos.map((file, index) => ({
        id: `photo-${Date.now()}-${index}`,
        fileName: file.name,
        fileUrl: URL.createObjectURL(file),
        thumbnailUrl: URL.createObjectURL(file),
        uploadedAt: new Date().toISOString(),
        uploadedBy: 'Current User',
      })),
    };

    setArtworkRequests(prev => [newRequest, ...prev]);
  };

  const handleContactClick = (item: any) => {
    setCurrentContactItem(item);
    setContactDialogOpen(true);
  };

  const handleSendCustomerContact = (questions: any[]) => {
    if (!currentContactItem) return;

    handleApiCall(async () => {
      await apiClient.post(`/orders/${orderData.id}/customer-contact`, {
        itemId: currentContactItem.id,
        questions,
      });
    }, 'Customer contact request sent successfully');

    setContactDialogOpen(false);
    setCurrentContactItem(null);
  };

  const sortLineItemsByNumber = (items: any[]) => {
    if (!items || !Array.isArray(items)) return [];

    return [...items].sort((a, b) => {
      const aNum = a.itemNumber ? parseInt(a.itemNumber.toString().replace(/\D/g, ''), 10) : 0;
      const bNum = b.itemNumber ? parseInt(b.itemNumber.toString().replace(/\D/g, ''), 10) : 0;

      return (isNaN(aNum) ? 0 : aNum) - (isNaN(bNum) ? 0 : bNum);
    });
  };

  return (
    <>
      <Grid2 container spacing={3} sx={{ mt: 2 }}>
        <Grid2 size={{ xs: 12, md: 3 }}>
          <Typography variant="body1" fontWeight="bold">
            Shopify Order Number
          </Typography>
          <Typography>{updatedOrderData?.shopifyOrderNumber}</Typography>
        </Grid2>

        <Grid2 size={{ xs: 12, md: 3 }}>
          <Typography variant="body1" fontWeight="bold">
            Order Date
          </Typography>
          <Typography>
            {updatedOrderData?.orderDate
              ? new Date(updatedOrderData.orderDate)
                  .toLocaleString('en-US', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false,
                  })
                  .replace(',', '')
              : ''}
          </Typography>
        </Grid2>

        <Grid2 size={{ xs: 12, md: 3 }}>
          <Typography variant="body1" fontWeight="bold">
            Status
          </Typography>
          <StatusChip status={updatedOrderData?.orderStatus} />
        </Grid2>

        <Grid2 size={{ xs: 12, md: 3 }}>
          <Typography variant="body1" fontWeight="bold">
            Priority
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
            {Array.isArray(updatedOrderData.priorities) &&
            updatedOrderData.priorities.length > 0 ? (
              updatedOrderData.priorities.map((priority: any, index: any) => (
                <Chip
                  key={index}
                  label={priority}
                  size="small"
                  sx={{
                    backgroundColor: priority.toUpperCase().includes('HOLIDAY')
                      ? '#ffcccc'
                      : priority.toUpperCase().includes('RUSH')
                        ? '#ffe0b2'
                        : '#ccc',
                    color: '#000',
                  }}
                />
              ))
            ) : (
              <Chip
                label="Standard"
                size="small"
                sx={{
                  backgroundColor: '#ccc',
                  color: '#000',
                }}
              />
            )}
          </Box>
        </Grid2>

        <Grid2 size={{ xs: 12, md: 3 }}>
          <Typography variant="body1" fontWeight="bold">
            Order Items Count
          </Typography>
          <Typography>{updatedOrderData?.itemCount}</Typography>
        </Grid2>

        <Grid2 size={{ xs: 12, md: 3 }}>
          <Typography variant="body1" fontWeight="bold">
            Customer Name
          </Typography>
          <Typography>
            {updatedOrderData.customerFirstName + ' ' + updatedOrderData.customerLastName}
          </Typography>
        </Grid2>

        <Grid2 size={{ xs: 12, md: 6 }}>
          <Typography variant="body1" fontWeight="bold">
            Customer Email
          </Typography>
          <Typography>{updatedOrderData?.customerEmail}</Typography>
        </Grid2>
      </Grid2>

      <Divider sx={{ my: 4 }} />

      <Typography variant="h6" component="div" sx={{ mb: 2 }}>
        Line Items
      </Typography>

      {updatedOrderData.lineItems &&
        sortLineItemsByNumber(updatedOrderData.lineItems).map((item: any, index: number) => (
          <Accordion
            key={item.id || index}
            expanded={expanded === `panel${index}`}
            onChange={handleChange(`panel${index}`)}
            sx={{ mb: 2 }}
          >
            <AccordionSummary
              expandIcon={<ExpandMoreIcon />}
              aria-controls={`panel${index}-content`}
              id={`panel${index}-header`}
              sx={{
                backgroundColor: item.flagged ? 'rgba(255, 235, 235, 0.5)' : 'background.default',
                '&.Mui-expanded': {
                  borderBottom: '1px solid rgba(0, 0, 0, 0.12)',
                },
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', gap: 46 }}>
                <Typography variant="body1" fontWeight="bold">
                  Item #: CC-{item.itemNumber}
                </Typography>
                <Typography variant="body1" sx={{ flexGrow: 1 }}>
                  SKU: {item?.productSku?.sku || 'Not Provided'}
                </Typography>
                <Tooltip title={item.flagged ? 'Unflag this item' : 'Flag this item'}>
                  <Box
                    sx={{
                      position: 'relative',
                      display: 'inline-flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Box
                      sx={{
                        position: 'absolute',
                        width: '28px',
                        height: '28px',
                        borderRadius: '50%',
                        backgroundColor: item.flagged ? 'error.light' : 'success.light',
                      }}
                    />
                    <IconButton
                      size="small"
                      onClick={e => {
                        e.stopPropagation();
                        handleFlagClick(item);
                      }}
                      disabled={!canFlagLineItem()}
                      sx={{
                        color: 'text.primary',
                        position: 'relative',
                        zIndex: 1,
                        '&:hover': {
                          backgroundColor: 'transparent',
                        },
                      }}
                    >
                      <FlagIcon />
                    </IconButton>
                  </Box>
                </Tooltip>
              </Box>
            </AccordionSummary>

            <AccordionDetails>
              <Grid2 container spacing={3} sx={{ mt: 2 }}>
                <Grid2 container spacing={3}>
                  {/* First row */}
                  <Grid2 size={{ xs: 6, md: 3 }}>
                    <Typography variant="body1" fontWeight="bold">
                      Item Number
                    </Typography>
                    <Typography>CC-{item?.itemNumber}</Typography>
                  </Grid2>

                  <Grid2 size={{ xs: 6, md: 3 }}>
                    <Typography variant="body1" fontWeight="bold">
                      SKU
                    </Typography>
                    <Typography>{item?.productSku?.sku}</Typography>
                  </Grid2>

                  <Grid2 size={{ xs: 6, md: 3 }}>
                    <Typography variant="body1" fontWeight="bold">
                      Quantity
                    </Typography>
                    <Typography>{item.quantity}</Typography>
                  </Grid2>

                  <Grid2 size={{ xs: 6, md: 3 }}>
                    <Typography variant="body1" fontWeight="bold">
                      Status
                    </Typography>
                    <StatusChip status={item?.status} variant="item" />
                  </Grid2>

                  {/* Second row */}
                  <Grid2 size={{ xs: 6, md: 3 }}>
                    <Typography variant="body1" fontWeight="bold">
                      Remake Status
                    </Typography>
                    <Chip
                      label={item?.isRemake ? 'Yes' : 'No'}
                      size="small"
                      sx={{
                        mt: 1,
                        bgcolor: item.isRemake ? '#e1bee7' : '#bbdefb',
                        color: '#000',
                      }}
                    />
                  </Grid2>

                  <Grid2 size={{ xs: 6, md: 3 }}>
                    <Typography variant="body1" fontWeight="bold">
                      Priority
                    </Typography>
                    <Chip
                      label={item?.priority || 'None'}
                      size="small"
                      sx={{
                        bgcolor: item?.priority?.includes('Rush') ? '#ffe0b2' : '#e0e0e0',
                        color: '#000',
                      }}
                    />
                  </Grid2>

                  {(item.remakeReason || (item.remakeReasons && item.remakeReasons.length > 0)) && (
                    <Grid2 size={{ xs: 6, md: 3 }}>
                      <Typography variant="body1" fontWeight="bold">
                        Remake Reasons
                      </Typography>
                      <Typography>
                        {item.remakeReasons && Array.isArray(item.remakeReasons)
                          ? item.remakeReasons.join(', ')
                          : item.remakeReason
                            ? Array.isArray(item.remakeReason)
                              ? item.remakeReason.join(', ')
                              : item.remakeReason
                            : 'Not specified'}
                      </Typography>
                    </Grid2>
                  )}

                  {item.detailedRemakeReason && item.detailedRemakeReason.length > 0 && (
                    <Grid2 size={{ xs: 6, md: 3 }}>
                      <Typography variant="body1" fontWeight="bold">
                        Detailed Remake Reasons
                      </Typography>
                      <Typography>
                        {Array.isArray(item.detailedRemakeReason)
                          ? item.detailedRemakeReason.join(', ')
                          : item.detailedRemakeReason}
                      </Typography>
                    </Grid2>
                  )}

                  {item.flagged && (
                    <Grid2 size={{ xs: 6, md: 3 }}>
                      <Typography variant="body1" fontWeight="bold">
                        Flag Reason
                      </Typography>
                      <Typography color="error">{item.flagReason || 'Item is flagged'}</Typography>
                    </Grid2>
                  )}

                  {item.type === 'addon' && (
                    <Grid2 size={{ xs: 6, md: 3 }}>
                      <Typography variant="body1" fontWeight="bold">
                        Add-on Type
                      </Typography>
                      <Typography>{item.addonType || 'Standard'}</Typography>
                    </Grid2>
                  )}

                  {item.shopifyVariant && (
                    <Grid2 size={{ xs: 6, md: 3 }}>
                      <Typography variant="body1" fontWeight="bold">
                        Shopify Variant
                      </Typography>
                      <Typography>{item.shopifyVariant}</Typography>
                    </Grid2>
                  )}

                  {item.customOptions && (
                    <Grid2 size={{ xs: 6, md: 3 }}>
                      <Typography variant="body1" fontWeight="bold" sx={{ mb: 1 }}>
                        Custom Options
                      </Typography>
                      <Box sx={{ pl: 2 }}>
                        {Object.entries(item.customOptions).map(([key, value]) => (
                          <Typography key={key} sx={{ mb: 0.5 }}>
                            <strong>{key}:</strong> {String(value)}
                          </Typography>
                        ))}
                      </Box>
                    </Grid2>
                  )}
                </Grid2>

                {/* Artwork and Images Section */}
                <Grid2 container spacing={3} sx={{ mt: 2 }}>
                  <Grid2 size={{ xs: 12 }}>
                    <ArtfileSection
                      item={item}
                      handleFileUpload={handleFileUpload}
                      formatDate={formatDate}
                    />
                  </Grid2>

                  <Grid2 size={{ xs: 12 }}>
                    <CustomerImagesSection metadata={item?.metadata} formatDate={formatDate} />
                  </Grid2>

                  {/* Action Buttons moved here - directly below Customer Images */}
                  <Grid2
                    size={12}
                    sx={{
                      display: 'flex',
                      justifyContent: 'flex-end',
                      flexWrap: 'wrap',
                      gap: 2,
                      mt: 2,
                    }}
                  >
                    <Button
                      variant="contained"
                      startIcon={<EditIcon />}
                      onClick={() => handleEditClick(item)}
                      disabled={!isItemEditable(item)}
                      size="large"
                    >
                      Edit Item
                    </Button>

                    <Button
                      variant="outlined"
                      color="error"
                      startIcon={<CancelIcon />}
                      onClick={() => handleCancelClick(item)}
                      disabled={!isItemCancellable(item)}
                      size="large"
                    >
                      Cancel Item
                    </Button>

                    <Button
                      variant="outlined"
                      color="warning"
                      startIcon={<RestartAltIcon />}
                      onClick={() => handleRemakeClick(item)}
                      disabled={!isItemRemakeable(item)}
                      size="large"
                    >
                      Remake Item
                    </Button>

                    <Button
                      variant="outlined"
                      color="info"
                      startIcon={<ContactSupportIcon />}
                      onClick={() => handleContactClick(item)}
                      disabled={!canInitiateCustomerContact()}
                      size="large"
                    >
                      Customer Contact Needed
                    </Button>
                  </Grid2>

                  {item.artworkRequired && (
                    <Grid2 size={{ xs: 12 }}>
                      <ArtworkRequestsSection
                        itemId={item.id}
                        requests={item.artworkRequests}
                        onAddRequest={handleAddArtworkRequest}
                      />
                    </Grid2>
                  )}
                </Grid2>

                {/* Remove the original Actions Section */}
              </Grid2>
            </AccordionDetails>
          </Accordion>
        ))}

      <EditItemDialog
        open={editDialogOpen}
        onClose={handleCloseDialog}
        currentItem={currentEditItem}
        onSave={handleSaveItem}
      />

      <CancelItemDialog
        open={cancelDialogOpen}
        onClose={handleCloseDialog}
        currentItem={currentEditItem}
        onCancel={handleCancelItem}
      />

      <RemakeItemDialog
        open={remakeDialogOpen}
        onClose={handleCloseDialog}
        currentItem={currentEditItem}
        onSubmit={handleSubmitRemake}
      />

      <FlagItemDialog
        open={flagDialogOpen}
        onClose={handleCloseDialog}
        currentItem={currentEditItem}
        onFlag={handleFlagItem}
      />

      <ConfirmationDialog
        open={confirmDialogOpen}
        title="Remake Item"
        message={
          <DialogContentText>
            Are you sure you want to remake this item? This will create a new production request for
            the item.
          </DialogContentText>
        }
        confirmLabel="Remake"
        cancelLabel="Cancel"
        confirmColor="warning"
        onConfirm={handleConfirmAction}
        onCancel={() => setConfirmDialogOpen(false)}
      />

      <CustomerContactDialog
        open={contactDialogOpen}
        onClose={() => setContactDialogOpen(false)}
        onSend={handleSendCustomerContact}
        customerImages={currentContactItem?.customerImages || []}
        artFiles={currentContactItem?.artFiles || []}
        itemId={currentContactItem?.id || ''}
      />
    </>
  );
};

export default OrderDetailsTab;
