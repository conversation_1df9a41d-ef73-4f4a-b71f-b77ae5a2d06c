import { useState, useEffect } from 'react';
import { Box, FormControl, TextField, Typography, CircularProgress } from '@mui/material';
import CustomDialog from '@/components/CustomDialog';
import { EditItemDialogProps } from '@/types/edit-item.types';
import ConfirmationDialog from '@/components/ConfirmationDialog';
import ImagePreviewDialog from '@/components/ImagePreviewDialog';
import ImageUploadSection from '@/components/ImageUploadSection';
import SearchableSelect from '@/components/SearchableSelect';
import apiClient from '@/utils/axios';
import { toast } from 'react-toastify';

export const uploadImage = async (file: File): Promise<string> => {
  try {
    const formData = new FormData();
    formData.append('file', file);

    const response = await apiClient.post('/attachments/upload-image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    if (response.data && response.data.url) {
      return response.data.url;
    } else {
      throw new Error('Invalid response from server');
    }
  } catch (error) {
    throw error;
  }
};

const EditItemDialog = ({ open, onClose, currentItem, onSave }: EditItemDialogProps) => {
  const [editSkuId, setEditSkuId] = useState<string>(currentItem?.productSku?.id || '');
  const [editSkuKey, setEditSkuKey] = useState<string>(currentItem?.productSku?.sku || '');
  const [selectedSkus, setSelectedSkus] = useState<Array<{ key: string; value: string }>>([]);
  const [editQuantity, setEditQuantity] = useState(currentItem?.quantity || 1);
  const [quantityError, setQuantityError] = useState<string>('');
  const [uploadedImages, setUploadedImages] = useState<File[]>([]);
  const [uploadedImageUrls, setUploadedImageUrls] = useState<string[]>([]);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [alertDialogOpen, setAlertDialogOpen] = useState(false);
  const [maxImages, setMaxImages] = useState<number>(1);

  const extractPetsCount = (skuKey: string): number => {
    const petsMatch = skuKey.match(/(\d+)PETS/i);
    if (petsMatch && petsMatch[1]) {
      const count = parseInt(petsMatch[1], 10);
      return isNaN(count) ? 1 : count;
    }
    return 1;
  };

  useEffect(() => {
    if (open && currentItem) {
      const skuKey = currentItem?.productSku?.sku || '';
      setEditSkuId(currentItem?.productSku?.id || '');
      setEditSkuKey(skuKey);
      setSelectedSkus(
        currentItem?.productSku?.id
          ? [{ key: skuKey, value: currentItem?.productSku?.id || '' }]
          : [],
      );
      setEditQuantity(currentItem.quantity || 1);
      setQuantityError('');
      setUploadedImages([]);
      setUploadedImageUrls([]);
      setHasChanges(false);
      setMaxImages(extractPetsCount(skuKey));
    }
  }, [open, currentItem]);

  useEffect(() => {
    if (open && currentItem) {
      const skuChanged = editSkuId !== currentItem?.productSku?.id;
      const quantityChanged = editQuantity !== currentItem.quantity;
      const imagesChanged = uploadedImages.length > 0;
      setHasChanges(skuChanged || quantityChanged || imagesChanged);
    }
  }, [editSkuId, editQuantity, uploadedImages, currentItem, open]);

  useEffect(() => {
    if (uploadedImages.length > maxImages) {
      const trimmedImages = uploadedImages.slice(0, maxImages);
      setUploadedImages(trimmedImages);
      setUploadedImageUrls(prev => prev.slice(0, maxImages));
    }
  }, [maxImages, uploadedImages.length]);

  const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;

    if (inputValue === '') {
      setEditQuantity(0);
      setQuantityError('Quantity must be at least 1');
      return;
    }

    const value = parseInt(inputValue, 10);

    if (isNaN(value)) {
      setQuantityError('Please enter a valid number');
    } else if (value < 1) {
      setQuantityError('Quantity must be at least 1');
    } else {
      setQuantityError('');
      setEditQuantity(value);
    }
  };

  const handleAddImages = async (newImages: File[]) => {
    if (newImages.length === 0) return;

    setIsUploading(true);

    try {
      const remainingSlots = maxImages - uploadedImages.length;
      const imagesToUpload = newImages.slice(0, remainingSlots);
      const uploadPromises = imagesToUpload.map(file => uploadImage(file));
      const urls = await Promise.all(uploadPromises);
      setUploadedImages(prev => [...prev, ...imagesToUpload]);
      setUploadedImageUrls(prev => [...prev, ...urls]);

      toast.success(`${imagesToUpload.length} image(s) uploaded successfully`);
    } catch (error) {
      toast.error('Failed to upload images. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  const handleRemoveImage = (index: number) => {
    setUploadedImages(prev => prev.filter((_, i) => i !== index));
    setUploadedImageUrls(prev => prev.filter((_, i) => i !== index));
  };

  const handlePreviewImage = (file: File) => {
    const index = uploadedImages.findIndex(img => img === file);
    if (index !== -1 && uploadedImageUrls[index]) {
      setPreviewImage(uploadedImageUrls[index]);
    } else {
      setPreviewImage(URL.createObjectURL(file));
    }
  };

  const handleClosePreview = () => {
    setPreviewImage(null);
  };

  const handleSave = () => {
    if (editQuantity < 1) {
      setQuantityError('Quantity must be at least 1');
      return;
    }

    onSave(
      editSkuId,
      editQuantity,
      uploadedImages.length > 0 ? uploadedImages : undefined,
      uploadedImageUrls.length > 0 ? uploadedImageUrls : undefined,
    );
  };

  const handleCancel = () => {
    if (hasChanges) {
      setConfirmDialogOpen(true);
    } else {
      onClose();
    }
  };

  const handleConfirmCancel = () => {
    setConfirmDialogOpen(false);
    onClose();
  };

  return (
    <>
      <CustomDialog
        open={open}
        title="Edit Line Item"
        confirmLabel="Save Item"
        cancelLabel="Cancel"
        confirmColor="primary"
        disableConfirm={
          !hasChanges || !!quantityError || isUploading || !editSkuId || editQuantity < 1
        }
        onConfirm={handleSave}
        onCancel={handleCancel}
      >
        <Box sx={{ mt: 2 }}>
          <Typography variant="subtitle2" gutterBottom sx={{ mb: 3 }}>
            Item SKU: {currentItem?.productSku?.sku}
          </Typography>

          <FormControl fullWidth sx={{ mb: 3 }}>
            <SearchableSelect
              attr={{
                key: 'sku',
                label: 'Item SKU',
                fetch_db: true,
                filter_key: 'sku',
                type: 'multi_select',
                usage: 'filter',
                backend_key: 'id',
                secondary_key: 'sku',
              }}
              value={selectedSkus}
              handleValueChange={(field: string, newValue: any) => {
                if (Array.isArray(newValue)) {
                  if (newValue.length > 1) {
                    setAlertDialogOpen(true);
                    return;
                  }

                  setSelectedSkus(newValue);

                  if (newValue.length > 0) {
                    const firstItem = newValue[0];
                    setEditSkuId(firstItem.value);
                    setEditSkuKey(firstItem.key);

                    setMaxImages(extractPetsCount(firstItem.key));
                  } else {
                    setEditSkuId('');
                    setEditSkuKey('');
                    setMaxImages(1);
                  }
                }
              }}
              multiple={true}
              serachUrl="product-sku"
              sx={true}
            />
          </FormControl>

          <FormControl fullWidth sx={{ mb: 3 }}>
            <TextField
              label="Quantity"
              type="number"
              value={editQuantity === 0 ? '' : editQuantity}
              onChange={handleQuantityChange}
              error={!!quantityError}
              helperText={quantityError}
              InputProps={{
                inputProps: { min: 1 },
              }}
            />
          </FormControl>

          <ImageUploadSection
            images={uploadedImages}
            onAddImages={handleAddImages}
            onRemoveImage={handleRemoveImage}
            onPreviewImage={handlePreviewImage}
            maxImages={maxImages}
            title="Upload Images"
            helperText={
              maxImages === 1
                ? 'You can upload 1 image for this item'
                : `You can upload up to ${maxImages} images for this item`
            }
            showPetLabels={true}
          />

          {isUploading && (
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
              <CircularProgress size={24} />
              <Typography variant="body2" sx={{ ml: 1 }}>
                Uploading images...
              </Typography>
            </Box>
          )}
        </Box>
      </CustomDialog>

      <ImagePreviewDialog
        open={!!previewImage}
        onClose={handleClosePreview}
        imageUrl={previewImage}
        title="Image Preview"
      />

      <ConfirmationDialog
        open={confirmDialogOpen}
        title="Discard Changes"
        message="You have unsaved changes. Are you sure you want to discard them?"
        confirmLabel="Discard"
        cancelLabel="Continue Editing"
        confirmColor="error"
        onConfirm={handleConfirmCancel}
        onCancel={() => setConfirmDialogOpen(false)}
      />

      <ConfirmationDialog
        open={alertDialogOpen}
        title="Selection Limit"
        message="Only one item can be selected at a time. Please remove the current selection before adding a new one."
        confirmLabel="OK"
        cancelLabel=""
        confirmColor="primary"
        onConfirm={() => setAlertDialogOpen(false)}
        onCancel={() => setAlertDialogOpen(false)}
      />
    </>
  );
};

export default EditItemDialog;
