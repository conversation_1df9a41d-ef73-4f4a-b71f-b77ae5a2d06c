import { useState } from 'react';
import {
  Typo<PERSON>,
  Box,
  Paper,
  Button,
  Dialog,
  DialogContent,
  DialogTitle,
  IconButton,
  Link,
  Chip,
} from '@mui/material';
import Grid2 from '@mui/material/Grid2';
import DownloadIcon from '@mui/icons-material/Download';
import CloseIcon from '@mui/icons-material/Close';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import { ArtfileSectionProps } from '@/types/artfile.types';

const ArtfileSection = ({ item, handleFileUpload, formatDate }: ArtfileSectionProps) => {
  const [selectedFile, setSelectedFile] = useState<any | null>(null);

  const handleFileClick = (file: any) => {
    setSelectedFile(file);
  };

  const handleCloseDialog = () => {
    setSelectedFile(null);
  };

  const handleDownload = (file: any, e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
    }
    // Create a link element
    const link = document.createElement('a');
    link.href = file.fileUrl;
    link.download = file.fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const artworkFiles = item?.artworkFiles || [];

  return (
    <Paper sx={{ p: 2, mt: 2, bgcolor: 'background.paper' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="subtitle1" fontWeight="bold">
          Artwork Files
        </Typography>
        <Button
          variant="contained"
          startIcon={<CloudUploadIcon />}
          size="small"
          component="label"
          disabled={!item?.artworkRequired}
        >
          Upload Artwork
          <input
            type="file"
            hidden
            accept="image/*,.pdf,.ai,.psd"
            onChange={e => handleFileUpload(e, item.id)}
          />
        </Button>
      </Box>

      {artworkFiles.length > 0 ? (
        <Grid2 container spacing={2}>
          {artworkFiles.map((file: any, index: number) => (
            <Grid2 size={{ xs: 12, sm: 6, md: 4 }} key={file.id || index}>
              <Paper
                elevation={1}
                sx={{
                  p: 2,
                  bgcolor: 'background.default',
                  height: '100%',
                }}
              >
                {/* File Title */}
                <Typography variant="subtitle2" gutterBottom>
                  {file.fileType === 'artwork' ? 'Artwork File' : 'Template File'}
                </Typography>
                <Box
                  sx={{
                    cursor: 'pointer',
                    overflow: 'hidden',
                    borderRadius: 1,
                    mb: 1,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    bgcolor: 'rgba(0, 0, 0, 0.05)',
                    p: 1,
                    height: 150,
                  }}
                  onClick={() => handleFileClick(file)}
                >
                  {file.thumbnailUrl ? (
                    <img
                      src={file.thumbnailUrl}
                      alt={file.fileName}
                      style={{
                        maxWidth: '100%',
                        maxHeight: '140px',
                        objectFit: 'contain',
                      }}
                    />
                  ) : (
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        height: '100%',
                        width: '100%',
                      }}
                    >
                      <Typography variant="body2" color="text.secondary" align="center">
                        {file.fileName.split('.').pop()?.toUpperCase()} File
                      </Typography>
                      <Typography variant="caption" color="text.secondary" align="center">
                        (No preview available)
                      </Typography>
                    </Box>
                  )}
                </Box>
                <Typography
                  variant="caption"
                  sx={{
                    display: 'block',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    mb: 1,
                  }}
                  title={file.fileName}
                >
                  {file.fileName}
                </Typography>
                <Box
                  sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
                >
                  <Chip
                    label={file.status || 'Uploaded'}
                    size="small"
                    color={
                      file.status === 'Approved'
                        ? 'success'
                        : file.status === 'Rejected'
                          ? 'error'
                          : 'default'
                    }
                    sx={{ mr: 1 }}
                  />
                  <Typography variant="caption" color="text.secondary">
                    {formatDate(file.uploadedAt)}
                  </Typography>
                </Box>
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<DownloadIcon />}
                  onClick={e => handleDownload(file, e)}
                  fullWidth
                  sx={{ mt: 1 }}
                >
                  Download
                </Button>
              </Paper>
            </Grid2>
          ))}
        </Grid2>
      ) : (
        <Box sx={{ py: 2, textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            {item?.artworkRequired
              ? 'No artwork files have been uploaded yet'
              : 'This item does not require artwork files'}
          </Typography>
        </Box>
      )}

      {/* Full-size file dialog */}
      <Dialog open={!!selectedFile} onClose={handleCloseDialog} maxWidth="lg" fullWidth>
        {selectedFile && (
          <>
            <DialogTitle
              sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Link
                  component="button"
                  variant="subtitle1"
                  onClick={() => handleDownload(selectedFile)}
                  underline="hover"
                  sx={{ fontWeight: 'medium', cursor: 'pointer' }}
                >
                  {selectedFile.fileName}
                </Link>
                <Chip
                  label={selectedFile.status || 'Uploaded'}
                  size="small"
                  color={
                    selectedFile.status === 'Approved'
                      ? 'success'
                      : selectedFile.status === 'Rejected'
                        ? 'error'
                        : 'default'
                  }
                />
                <Typography variant="caption" color="text.secondary">
                  Uploaded {formatDate(selectedFile.uploadedAt)}
                </Typography>
              </Box>
              <IconButton edge="end" onClick={handleCloseDialog}>
                <CloseIcon />
              </IconButton>
            </DialogTitle>
            <DialogContent sx={{ textAlign: 'center', p: 2 }}>
              {selectedFile.thumbnailUrl ? (
                <img
                  src={selectedFile.fileUrl || selectedFile.thumbnailUrl}
                  alt={selectedFile.fileName}
                  style={{
                    maxWidth: '100%',
                    maxHeight: 'calc(100vh - 200px)',
                    objectFit: 'contain',
                  }}
                />
              ) : (
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    height: '300px',
                    width: '100%',
                    bgcolor: 'rgba(0, 0, 0, 0.05)',
                    borderRadius: 1,
                  }}
                >
                  <Typography variant="h6" component="div" color="text.secondary" gutterBottom>
                    {selectedFile.fileName.split('.').pop()?.toUpperCase()} File
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    No preview available for this file type
                  </Typography>
                  <Button
                    variant="contained"
                    startIcon={<DownloadIcon />}
                    onClick={() => handleDownload(selectedFile)}
                    sx={{ mt: 2 }}
                  >
                    Download File
                  </Button>
                </Box>
              )}
            </DialogContent>
          </>
        )}
      </Dialog>
    </Paper>
  );
};

export default ArtfileSection;
