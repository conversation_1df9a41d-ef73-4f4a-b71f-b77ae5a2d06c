'use client';
import { createContext, ReactNode, useContext, useEffect, useState } from 'react';
import { AppAbility, defineAbilityFor } from './ability'; // Assuming defineAbilityFor is correctly set up
import { useSession } from 'next-auth/react';

export const AbilityContext = createContext<AppAbility | null>(null);

export const AbilityProvider = ({ children }: { children: ReactNode }) => {
  const { data: session, status } = useSession();
  const [ability, setAbility] = useState<AppAbility | null>(null);

  useEffect(() => {
    // console.log(session);
    if (status === 'authenticated' && session) {
      const userAbility = defineAbilityFor({ roles: session.user?.roles });
      setAbility(userAbility);
    }
  }, [status, session]);

  if (!ability) {
    return;
  }

  return <AbilityContext.Provider value={ability}>{children}</AbilityContext.Provider>;
};

export const useAbility = () => useContext(AbilityContext);
